import request from '@/utils/request'

/**
 * 分支机构微信粉丝管理API
 */

// 获取粉丝统计数据
export function getFansStats(branchId) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/stats`,
    method: 'get'
  })
}

// 获取粉丝列表
export function getFansList(branchId, params = {}) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans`,
    method: 'get',
    params
  })
}

// 获取粉丝详情
export function getFansDetail(branchId, openid) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/${openid}`,
    method: 'get'
  })
}

// 同步粉丝数据
export function syncFansData(branchId) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/sync`,
    method: 'post'
  })
}

// 获取粉丝分组
export function getFansGroups(branchId) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/groups`,
    method: 'get'
  })
}

// 创建粉丝分组
export function createFansGroup(branchId, data) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/groups`,
    method: 'post',
    data
  })
}

// 更新粉丝备注
export function updateFansRemark(branchId, openid, remark) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/${openid}/remark`,
    method: 'put',
    data: { remark }
  })
}

// 移动粉丝到分组
export function moveFansToGroup(branchId, openid, groupId) {
  return request({
    url: `/api/admin/v1/branches/${branchId}/wechat/fans/${openid}/group`,
    method: 'put',
    data: { group_id: groupId }
  })
} 