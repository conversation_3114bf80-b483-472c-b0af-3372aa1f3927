<template>
  <div class="branch-wechat-menu">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">微信自定义菜单</h1>
        <div class="branch-info">
          <span class="branch-name">{{ branchInfo.name }}</span>
          <span class="wechat-status" :class="wechatStatusClass">
            {{ wechatStatusText }}
          </span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-wrapper">
      <el-loading text="正在加载菜单数据..." />
    </div>

    <!-- 主要内容 -->
    <div v-else class="main-content">
      <!-- 操作按钮区域 -->
      <div class="action-bar">
        <div class="left-actions">
          <el-button 
            type="primary" 
            @click="addMenu" 
            :disabled="menus.length >= 3"
            icon="Plus"
          >
            添加菜单 ({{ menus.length }}/3)
          </el-button>
          <el-button 
            @click="loadTemplates" 
            icon="Document"
          >
            选择模板
          </el-button>
        </div>
        <div class="right-actions">
          <el-button 
            @click="syncFromWechat" 
            :loading="syncing"
            icon="Refresh"
          >
            从微信同步
          </el-button>
          <el-button 
            type="success" 
            @click="saveMenu" 
            :loading="saving"
            icon="Check"
          >
            保存配置
          </el-button>
          <el-button 
            type="warning" 
            @click="publishToWechat" 
            :loading="publishing"
            icon="Upload"
          >
            发布到微信
          </el-button>
        </div>
      </div>

      <!-- 菜单配置区域 -->
      <div class="menu-config-area">
        <div class="menu-list">
          <div 
            v-for="(menu, index) in menus" 
            :key="index"
            class="menu-item"
            :class="{ active: activeMenuIndex === index }"
            @click="selectMenu(index)"
          >
            <div class="menu-header">
              <div class="menu-info">
                <span class="menu-name">{{ menu.name || `菜单 ${index + 1}` }}</span>
                <span class="menu-type">{{ getMenuTypeText(menu.type) }}</span>
              </div>
              <div class="menu-actions">
                <el-button 
                  type="danger" 
                  size="small" 
                  text 
                  @click.stop="removeMenu(index)"
                  icon="Delete"
                >
                  删除
                </el-button>
              </div>
            </div>
            
            <!-- 子菜单 -->
            <div v-if="menu.sub_button && menu.sub_button.length > 0" class="sub-menus">
              <div 
                v-for="(subMenu, subIndex) in menu.sub_button" 
                :key="subIndex"
                class="sub-menu-item"
              >
                <span class="sub-menu-name">{{ subMenu.name }}</span>
                <span class="sub-menu-type">{{ getMenuTypeText(subMenu.type) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 菜单编辑表单 -->
        <div class="menu-form" v-if="currentMenu">
          <el-card>
            <template #header>
              <div class="form-header">
                <span>编辑菜单</span>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="addSubMenu"
                  :disabled="currentMenu.sub_button && currentMenu.sub_button.length >= 5"
                >
                  添加子菜单 ({{ (currentMenu.sub_button || []).length }}/5)
                </el-button>
              </div>
            </template>

            <!-- 菜单名称 -->
            <el-form-item label="菜单名称">
              <el-input 
                v-model="currentMenu.name" 
                placeholder="请输入菜单名称"
                maxlength="16"
                show-word-limit
              />
            </el-form-item>

            <!-- 菜单类型 -->
            <el-form-item label="菜单类型">
              <el-select v-model="currentMenu.type" placeholder="请选择菜单类型" @change="onMenuTypeChange">
                <el-option label="点击事件" value="click" />
                <el-option label="跳转链接" value="view" />
                <el-option label="小程序" value="miniprogram" />
                <el-option label="扫码推事件" value="scancode_push" />
                <el-option label="扫码带提示" value="scancode_waitmsg" />
                <el-option label="系统拍照发图" value="pic_sysphoto" />
                <el-option label="拍照或者相册发图" value="pic_photo_or_album" />
                <el-option label="微信相册发图" value="pic_weixin" />
                <el-option label="发送位置" value="location_select" />
              </el-select>
            </el-form-item>

            <!-- 根据类型显示不同的配置项 -->
            <template v-if="currentMenu.type === 'click'">
              <el-form-item label="事件KEY">
                <el-input 
                  v-model="currentMenu.key" 
                  placeholder="请输入事件KEY"
                />
              </el-form-item>
            </template>

            <template v-if="currentMenu.type === 'view'">
              <el-form-item label="链接地址">
                <el-input 
                  v-model="currentMenu.url" 
                  placeholder="请输入链接地址"
                  type="url"
                />
              </el-form-item>
            </template>

            <template v-if="currentMenu.type === 'miniprogram'">
              <el-form-item label="小程序AppID">
                <el-input 
                  v-model="currentMenu.appid" 
                  placeholder="请输入小程序AppID"
                />
              </el-form-item>
              <el-form-item label="小程序页面路径">
                <el-input 
                  v-model="currentMenu.pagepath" 
                  placeholder="请输入页面路径"
                />
              </el-form-item>
              <el-form-item label="备用网页链接">
                <el-input 
                  v-model="currentMenu.url" 
                  placeholder="不支持小程序时的备用链接"
                />
              </el-form-item>
            </template>

            <!-- 子菜单编辑 -->
            <div v-if="currentMenu.sub_button && currentMenu.sub_button.length > 0" class="sub-menu-config">
              <el-divider>子菜单配置</el-divider>
              <div 
                v-for="(subMenu, subIndex) in currentMenu.sub_button" 
                :key="subIndex"
                class="sub-menu-form"
              >
                <div class="sub-menu-header">
                  <span>子菜单 {{ subIndex + 1 }}</span>
                  <el-button 
                    type="danger" 
                    size="small" 
                    text 
                    @click="removeSubMenu(subIndex)"
                  >
                    删除
                  </el-button>
                </div>

                <el-form-item label="名称">
                  <el-input 
                    v-model="subMenu.name" 
                    placeholder="请输入子菜单名称"
                    maxlength="16"
                  />
                </el-form-item>

                <el-form-item label="类型">
                  <el-select v-model="subMenu.type" placeholder="请选择类型">
                    <el-option label="点击事件" value="click" />
                    <el-option label="跳转链接" value="view" />
                    <el-option label="小程序" value="miniprogram" />
                  </el-select>
                </el-form-item>

                <template v-if="subMenu.type === 'click'">
                  <el-form-item label="事件KEY">
                    <el-input v-model="subMenu.key" placeholder="请输入事件KEY" />
                  </el-form-item>
                </template>

                <template v-if="subMenu.type === 'view'">
                  <el-form-item label="链接地址">
                    <el-input v-model="subMenu.url" placeholder="请输入链接地址" />
                  </el-form-item>
                </template>

                <template v-if="subMenu.type === 'miniprogram'">
                  <el-form-item label="小程序AppID">
                    <el-input v-model="subMenu.appid" placeholder="请输入小程序AppID" />
                  </el-form-item>
                  <el-form-item label="页面路径">
                    <el-input v-model="subMenu.pagepath" placeholder="请输入页面路径" />
                  </el-form-item>
                  <el-form-item label="备用链接">
                    <el-input v-model="subMenu.url" placeholder="备用网页链接" />
                  </el-form-item>
                </template>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 模板选择对话框 -->
    <el-dialog 
      v-model="templateDialogVisible" 
      title="选择菜单模板" 
      width="600px"
    >
      <div class="template-list">
        <div 
          v-for="template in templates" 
          :key="template.id"
          class="template-item"
          @click="applyTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-desc">{{ template.description }}</div>
        </div>
      </div>
      <template #footer>
        <el-button @click="templateDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// 响应式数据
const loading = ref(true)
const saving = ref(false)
const syncing = ref(false)
const publishing = ref(false)
const branchId = ref(null)
const branchInfo = ref({})
const wechatConfig = ref({})
const menus = ref([])
const activeMenuIndex = ref(0)
const templateDialogVisible = ref(false)
const templates = ref([])

// 路由
const route = useRoute()

// 计算属性
const currentMenu = computed(() => {
  return menus.value[activeMenuIndex.value] || null
})

const wechatStatusClass = computed(() => {
  if (!wechatConfig.value.appid) return 'status-error'
  return wechatConfig.value.status === 'active' ? 'status-success' : 'status-warning'
})

const wechatStatusText = computed(() => {
  if (!wechatConfig.value.appid) return '未配置'
  return wechatConfig.value.status === 'active' ? '已授权' : '未授权'
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const response = await axios.get(`/api/admin/v1/branches/${branchId.value}/wechat/menu`)
    
    if (response.data.code === 0) {
      branchInfo.value = response.data.data.branch || {}
      wechatConfig.value = response.data.data.wechat_config || response.data.data.wechat_account || {}
      menus.value = response.data.data.menus || []
      
      // 如果没有菜单，创建默认结构
      if (menus.value.length === 0) {
        createDefaultMenus()
      }
    } else {
      ElMessage.error(response.data.message || '加载数据失败')
    }
  } catch (error) {
    console.error('加载菜单数据失败:', error)
    ElMessage.error('加载数据失败')
    createDefaultMenus()
  } finally {
    loading.value = false
  }
}

const autoSyncFromWechat = async () => {
  // 只有在微信配置正常且没有本地菜单时才自动同步
  if (!wechatConfig.value || !wechatConfig.value.appid) {
    console.log('微信配置不完整，跳过自动同步')
    return
  }

  if (menus.value.length > 1 || (menus.value.length === 1 && menus.value[0].name !== '菜单1')) {
    console.log('已有自定义菜单，跳过自动同步')
    return
  }

  try {
    console.log('正在自动同步微信菜单...')
    const response = await axios.post(`/api/admin/v1/branches/${branchId.value}/wechat/menu/sync`)
    
    if (response.data.code === 0) {
      console.log('自动同步微信菜单成功')
      // 重新加载数据以获取同步后的菜单
      await loadData()
      ElMessage.success('已自动同步微信现有菜单')
    } else {
      console.log('自动同步失败:', response.data.message)
      // 同步失败不显示错误消息，因为可能是首次使用没有菜单
    }
  } catch (error) {
    console.log('自动同步微信菜单出错:', error)
    // 自动同步失败不显示错误消息，用户可以手动同步
  }
}

const createDefaultMenus = () => {
  menus.value = [
    { 
      name: '菜单1', 
      type: 'click', 
      key: 'menu_1',
      sub_button: []
    }
  ]
  activeMenuIndex.value = 0
}

const selectMenu = (index) => {
  activeMenuIndex.value = index
}

const addMenu = () => {
  if (menus.value.length >= 3) {
    ElMessage.warning('最多只能添加3个一级菜单')
    return
  }
  
  menus.value.push({
    name: `菜单${menus.value.length + 1}`,
    type: 'click',
    key: `menu_${menus.value.length + 1}`,
    sub_button: []
  })
  
  activeMenuIndex.value = menus.value.length - 1
}

const removeMenu = (index) => {
  ElMessageBox.confirm('确定要删除这个菜单吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    menus.value.splice(index, 1)
    if (activeMenuIndex.value >= menus.value.length) {
      activeMenuIndex.value = Math.max(0, menus.value.length - 1)
    }
  }).catch(() => {})
}

const addSubMenu = () => {
  if (!currentMenu.value) return
  
  if (!currentMenu.value.sub_button) {
    currentMenu.value.sub_button = []
  }
  
  if (currentMenu.value.sub_button.length >= 5) {
    ElMessage.warning('每个一级菜单最多只能添加5个子菜单')
    return
  }
  
  currentMenu.value.sub_button.push({
    name: `子菜单${currentMenu.value.sub_button.length + 1}`,
    type: 'click',
    key: `sub_menu_${Date.now()}`
  })
}

const removeSubMenu = (subIndex) => {
  if (currentMenu.value && currentMenu.value.sub_button) {
    currentMenu.value.sub_button.splice(subIndex, 1)
  }
}

const onMenuTypeChange = () => {
  if (!currentMenu.value) return
  
  // 清除不相关的字段
  if (currentMenu.value.type !== 'click') {
    delete currentMenu.value.key
  }
  if (currentMenu.value.type !== 'view' && currentMenu.value.type !== 'miniprogram') {
    delete currentMenu.value.url
  }
  if (currentMenu.value.type !== 'miniprogram') {
    delete currentMenu.value.appid
    delete currentMenu.value.pagepath
  }
}

const getMenuTypeText = (type) => {
  const typeMap = {
    'click': '点击事件',
    'view': '跳转链接',
    'miniprogram': '小程序',
    'scancode_push': '扫码推事件',
    'scancode_waitmsg': '扫码带提示',
    'pic_sysphoto': '系统拍照',
    'pic_photo_or_album': '拍照或相册',
    'pic_weixin': '微信相册',
    'location_select': '发送位置'
  }
  return typeMap[type] || type
}

const saveMenu = async () => {
  try {
    saving.value = true
    
    const response = await axios.post(`/api/admin/v1/branches/${branchId.value}/wechat/menu`, {
      menus: menus.value
    })
    
    if (response.data.code === 0) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('保存菜单失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const syncFromWechat = async () => {
  try {
    syncing.value = true
    
    const response = await axios.post(`/api/admin/v1/branches/${branchId.value}/wechat/menu/sync`)
    
    if (response.data.code === 0) {
      ElMessage.success('同步成功')
      await loadData()
    } else {
      ElMessage.warning(response.data.message || '同步失败')
    }
  } catch (error) {
    console.error('同步菜单失败:', error)
    ElMessage.error('同步失败')
  } finally {
    syncing.value = false
  }
}

const publishToWechat = async () => {
  try {
    await ElMessageBox.confirm('确定要发布菜单到微信吗？', '确认发布', {
      type: 'warning'
    })
    
    publishing.value = true
    
    const response = await axios.post(`/api/admin/v1/branches/${branchId.value}/wechat/menu/publish`)
    
    if (response.data.code === 0) {
      ElMessage.success('发布成功')
    } else {
      ElMessage.error(response.data.message || '发布失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('发布菜单失败:', error)
      ElMessage.error('发布失败')
    }
  } finally {
    publishing.value = false
  }
}

const loadTemplates = async () => {
  try {
    const response = await axios.get(`/api/admin/v1/branches/${branchId.value}/wechat/menu/templates`)
    
    if (response.data.code === 0) {
      templates.value = response.data.data || []
      templateDialogVisible.value = true
    } else {
      ElMessage.error('加载模板失败')
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  }
}

const applyTemplate = async (template) => {
  try {
    await ElMessageBox.confirm('应用模板将覆盖当前菜单配置，确定继续吗？', '确认应用', {
      type: 'warning'
    })
    
    const response = await axios.post(`/api/admin/v1/branches/${branchId.value}/wechat/menu/apply-template`, {
      template_id: template.id
    })
    
    if (response.data.code === 0) {
      ElMessage.success('应用模板成功')
      templateDialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(response.data.message || '应用模板失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应用模板失败:', error)
      ElMessage.error('应用模板失败')
    }
  }
}

// 组件挂载时执行
onMounted(async () => {
  branchId.value = route.params.branchId
  if (!branchId.value) {
    ElMessage.error('分支机构ID参数缺失')
    return
  }
  await loadData()
  // 页面加载完成后自动同步微信菜单
  await autoSyncFromWechat()
})
</script>

<style scoped>
.branch-wechat-menu {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.branch-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.branch-name {
  font-size: 16px;
  color: #666;
}

.wechat-status {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-warning {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.status-error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.loading-wrapper {
  background: white;
  padding: 100px;
  border-radius: 8px;
  text-align: center;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 12px;
}

.menu-config-area {
  display: flex;
  height: 600px;
}

.menu-list {
  width: 300px;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
}

.menu-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item.active {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-info {
  flex: 1;
}

.menu-name {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.menu-type {
  font-size: 12px;
  color: #999;
}

.sub-menus {
  margin-top: 12px;
  padding-left: 16px;
}

.sub-menu-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  font-size: 12px;
  color: #666;
}

.menu-form {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sub-menu-config {
  margin-top: 20px;
}

.sub-menu-form {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.sub-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.template-list {
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.template-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.template-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #999;
}
</style> 