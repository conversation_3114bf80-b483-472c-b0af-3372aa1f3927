<?php

namespace App\Http\Controllers\Mobile\Api\V1;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BranchVipController extends Controller
{
    /**
     * 获取分支机构VIP分红统计
     * 注意：分支机构VIP判定使用 vip_at 和 is_vip 字段，不是官方的 vip_paid_at 和 is_vip_paid
     */
    public function getDividendStats(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', 'current');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 计算月份范围
            if ($month === 'current') {
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                $monthLabel = Carbon::now()->format('Y年m月');
            } else {
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                $monthLabel = Carbon::now()->subMonth()->format('Y年m月');
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 查询分支机构VIP统计数据
            // 注意：使用分支机构的 vip_at 和 is_vip 字段
            $vipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startDate, $endDate])
                ->count();
            
            // 查询充值统计（如果有相关表）
            $rechargeCount = 0; // 暂时设为0，需要根据实际业务逻辑调整
            
            // 返回统计数据
            $stats = [
                'vipCount' => $vipCount,
                'rechargeCount' => $rechargeCount,
                'juniorVipTeams' => 0,
                'middleVipTeams' => 0,
                'seniorVipTeams' => 0,
                'totalSeniorDirectVips' => 0,
                'juniorRechargeTeams' => 0,
                'middleRechargeTeams' => 0,
                'seniorRechargeTeams' => 0,
                'totalSeniorDirectRecharges' => 0,
                'month' => $monthLabel,
                'monthValue' => $month,
                'totalAmount' => '0.00',
                'monthAmount' => '0.00',
                'pendingAmount' => '0.00'
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            Log::error("分支机构VIP分红统计查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取分支机构VIP团队数据
     */
    public function getTeamData(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', 'current');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 获取用户基本信息（分支机构用户可能没有设置branch_id，所以不强制检查）
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 查询团队VIP数据（使用分支机构字段）
            $totalVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            // 计算本月时间范围
            if ($month === 'current') {
                $startTime = date('Y-m-01 00:00:00');
                $endTime = date('Y-m-t 23:59:59');
            } else {
                // 上月
                $startTime = date('Y-m-01 00:00:00', strtotime('last month'));
                $endTime = date('Y-m-t 23:59:59', strtotime('last month'));
            }
            
            // 查询直推VIP数量（推荐人为当前用户）
            $directVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('referrer_id', $userId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            // 查询本月直推VIP数量（推荐人为当前用户，本月成为VIP）
            $monthDirectVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('referrer_id', $userId)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startTime, $endTime])
                ->count();
            
            // 查询本月团队新增VIP数量（整个分支机构本月新增）
            $monthVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startTime, $endTime])
                ->count();
            
            // 查询充值数据（这里需要根据实际的充值表结构来查询）
            // 暂时设为0，如果有充值表可以添加相应查询
            $monthRechargeCount = 0;
            $monthDirectRechargeCount = 0;
            
            $teamData = [
                'totalVipCount' => $totalVipCount,
                'directVipCount' => $directVipCount,
                'monthDirectVipCount' => $monthDirectVipCount,
                'monthVipCount' => $monthVipCount,
                'monthTeamVipCount' => $monthVipCount, // 团队本月新增VIP = 分支机构本月新增VIP
                'monthRechargeCount' => $monthRechargeCount,
                'monthDirectRechargeCount' => $monthDirectRechargeCount,
                'monthTeamRechargeCount' => $monthRechargeCount
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $teamData
            ]);
            
        } catch (\Exception $e) {
            Log::error("分支机构VIP团队数据查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取分支机构VIP时间信息（包含用户基本信息）
     */
    public function getTimeInfo(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 获取用户完整信息（使用分支机构字段）
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->where('branch_id', $branch->id)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }
            
            // 获取推荐人信息
            $referrerInfo = [
                'referrer_id' => 0,
                'referrer_name' => $branch->name ?? '华东' // 默认使用分支机构名称作为推荐人
            ];
            
            // 如果有推荐人ID，查询推荐人信息
            if ($user->referrer_id && $user->referrer_id > 0) {
                $referrer = DB::table('app_users')
                    ->where('id', $user->referrer_id)
                    ->first();
                    
                if ($referrer) {
                    $referrerInfo = [
                        'referrer_id' => $referrer->id,
                        'referrer_name' => $referrer->name ?? $referrer->nickname ?? '推荐人'
                    ];
                }
            }
            
            // 计算VIP到期时间
            $vipExpireTime = null;
            $vipDaysLeft = 0;
            $isVipActive = false;
            
            if ($user->is_vip && $user->vip_at) {
                $vipStartTime = Carbon::parse($user->vip_at);
                $vipExpireTime = $vipStartTime->copy()->addYear(); // 假设VIP有效期1年
                $vipDaysLeft = max(0, Carbon::now()->diffInDays($vipExpireTime, false));
                $isVipActive = $vipDaysLeft > 0;
            }
            
            $timeInfo = [
                'name' => $user->name ?? $user->nickname ?? '分支机构用户',
                'avatar' => $user->avatar ?? '/app/images/profile/default-avatar.png',
                'phone' => $user->phone ?? '',
                'isVip' => (bool)$user->is_vip,
                'isVipActive' => $isVipActive,
                'vipStartTime' => $user->vip_at ? Carbon::parse($user->vip_at)->format('Y-m-d H:i:s') : null,
                'vipExpireTime' => $vipExpireTime ? $vipExpireTime->format('Y-m-d H:i:s') : null,
                'vipDaysLeft' => $vipDaysLeft,
                'branchCode' => $branchCode,
                'branchName' => $branch->name ?? '未知分支机构',
                'userId' => $user->id,
                'referrer_id' => $referrerInfo['referrer_id'],
                'referrer_name' => $referrerInfo['referrer_name']
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $timeInfo
            ]);
            
        } catch (\Exception $e) {
            Log::error("分支机构VIP时间信息查询失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取分支机构VIP分红明细
     */
    public function getDividendDetail(Request $request)
    {
        try {
            $userId = $request->input('user_id');
            $branchCode = $request->input('branch_code');
            $month = $request->input('month', date('Y-m'));
            
            Log::info('获取分支机构VIP分红明细', [
                'user_id' => $userId,
                'branch_code' => $branchCode,
                'month' => $month
            ]);
            
            if (empty($userId) || empty($branchCode)) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少必要参数',
                    'data' => null
                ]);
            }
            
            // 首先根据branch_code获取branch_id
            $branch = DB::table('branch_organizations')
                ->where('code', $branchCode)
                ->first();
                
            if (!$branch) {
                return response()->json([
                    'code' => 1,
                    'message' => '分支机构不存在',
                    'data' => null
                ]);
            }
            
            // 获取用户信息（不强制要求branch_id匹配）
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->first();
                
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }

            // 获取分支机构分红配置
            $dividendConfig = DB::table('branch_dividend_configs')
                ->where('branch_id', $branch->id)
                ->where('is_active', 1)
                ->first();

            // 如果没有配置，使用默认配置
            if (!$dividendConfig) {
                $dividendConfig = (object) [
                    'branch_id' => $branch->id,
                    'vip_junior_requirement' => 3,
                    'vip_middle_requirement' => 10,
                    'vip_senior_requirement' => 30,
                    'vip_pool_amount' => 300.00,
                    'recharge_junior_requirement' => 10,
                    'recharge_middle_requirement' => 30,
                    'recharge_senior_requirement' => 80,
                    'recharge_pool_amount' => 15.00,
                    'is_active' => true,
                    'description' => '默认分红配置'
                ];
            }
            
            // 计算月份时间范围
            $startDate = $month . '-01';
            $endDate = date('Y-m-t', strtotime($startDate));
            
            // 获取分支机构本月新增VIP数量
            $monthVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->whereBetween('vip_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
                ->count();
            
            // 获取分支机构总VIP数量
            $totalVipCount = DB::table('app_users')
                ->where('branch_id', $branch->id)
                ->where('is_vip', 1)
                ->whereNotNull('vip_at')
                ->count();
            
            // 获取充值设备数量（暂时使用模拟数据）
            $monthRechargeCount = 0;
            $totalRechargeCount = 0;
            
            // 计算奖金池
            $vipPoolAmount = $monthVipCount * $dividendConfig->vip_pool_amount * 3;
            $rechargePoolAmount = $monthRechargeCount * $dividendConfig->recharge_pool_amount * 3;
            $totalPoolAmount = $vipPoolAmount + $rechargePoolAmount;
            
            // 模拟达标人数统计（实际应根据业务逻辑计算）
            $vipJuniorQualified = $monthVipCount >= $dividendConfig->vip_junior_requirement ? 1 : 0;
            $vipMiddleQualified = $monthVipCount >= $dividendConfig->vip_middle_requirement ? 1 : 0;
            $vipSeniorQualified = $monthVipCount >= $dividendConfig->vip_senior_requirement ? 1 : 0;
            
            $rechargeJuniorQualified = $monthRechargeCount >= $dividendConfig->recharge_junior_requirement ? 1 : 0;
            $rechargeMiddleQualified = $monthRechargeCount >= $dividendConfig->recharge_middle_requirement ? 1 : 0;
            $rechargeSeniorQualified = $monthRechargeCount >= $dividendConfig->recharge_senior_requirement ? 1 : 0;
            
            // 构建分红规则配置信息
            $dividendRules = [
                'vip_recruitment' => [
                    'junior' => [
                        'requirement' => $dividendConfig->vip_junior_requirement,
                        'pool_amount' => $dividendConfig->vip_pool_amount,
                        'description' => "团队本月新增VIP达到{$dividendConfig->vip_junior_requirement}人即可获得初级VIP分红"
                    ],
                    'middle' => [
                        'requirement' => $dividendConfig->vip_middle_requirement,
                        'pool_amount' => $dividendConfig->vip_pool_amount,
                        'description' => "团队本月新增VIP达到{$dividendConfig->vip_middle_requirement}人且本月有直推即可获得中级VIP分红"
                    ],
                    'senior' => [
                        'requirement' => $dividendConfig->vip_senior_requirement,
                        'pool_amount' => $dividendConfig->vip_pool_amount,
                        'description' => "团队本月新增VIP达到{$dividendConfig->vip_senior_requirement}人且本月有直推即可获得高级VIP分红（按直推占比分配）"
                    ]
                ],
                'device_recharge' => [
                    'junior' => [
                        'requirement' => $dividendConfig->recharge_junior_requirement,
                        'pool_amount' => $dividendConfig->recharge_pool_amount,
                        'description' => "团队本月新增充值设备达到{$dividendConfig->recharge_junior_requirement}台即可获得初级充值分红"
                    ],
                    'middle' => [
                        'requirement' => $dividendConfig->recharge_middle_requirement,
                        'pool_amount' => $dividendConfig->recharge_pool_amount,
                        'description' => "团队本月新增充值设备达到{$dividendConfig->recharge_middle_requirement}台且本月有直推即可获得中级充值分红"
                    ],
                    'senior' => [
                        'requirement' => $dividendConfig->recharge_senior_requirement,
                        'pool_amount' => $dividendConfig->recharge_pool_amount,
                        'description' => "团队本月新增充值设备达到{$dividendConfig->recharge_senior_requirement}台且本月有直推即可获得高级充值分红（按直推占比分配）"
                    ]
                ]
            ];
            
            // 构建返回数据
            $responseData = [
                // 分支机构基本信息
                'branch_info' => [
                    'id' => $branch->id,
                    'name' => $branch->name,
                    'code' => $branch->code
                ],
                
                // 分红配置规则
                'dividend_config' => [
                    'vip_junior_requirement' => $dividendConfig->vip_junior_requirement,
                    'vip_middle_requirement' => $dividendConfig->vip_middle_requirement,
                    'vip_senior_requirement' => $dividendConfig->vip_senior_requirement,
                    'vip_pool_amount' => $dividendConfig->vip_pool_amount,
                    'recharge_junior_requirement' => $dividendConfig->recharge_junior_requirement,
                    'recharge_middle_requirement' => $dividendConfig->recharge_middle_requirement,
                    'recharge_senior_requirement' => $dividendConfig->recharge_senior_requirement,
                    'recharge_pool_amount' => $dividendConfig->recharge_pool_amount,
                    'is_active' => $dividendConfig->is_active,
                    'description' => $dividendConfig->description ?? '分支机构分红配置'
                ],
                
                // 分红规则详细说明
                'dividend_rules' => $dividendRules,
                
                // 奖金池统计
                'pool_info' => [
                    'total_pool' => $totalPoolAmount,
                    'vip_pool' => $vipPoolAmount,
                    'device_pool' => $rechargePoolAmount,
                    'vip_count' => $monthVipCount,
                    'recharge_count' => $monthRechargeCount,
                    'calculation' => [
                        'vip_formula' => 'VIP分红池 = 新增VIP数量 × ' . number_format($dividendConfig->vip_pool_amount, 2) . '元 × 3',
                        'device_formula' => '充值分红池 = 新增充值数量 × ' . number_format($dividendConfig->recharge_pool_amount, 2) . '元 × 3'
                    ]
                ],
                
                // 平台统计
                'platform_stats' => [
                    'total_vip_count' => $totalVipCount,
                    'month_new_vip_count' => $monthVipCount,
                    'total_device_count' => $totalRechargeCount,
                    'month_new_device_count' => $monthRechargeCount
                ],
                
                // 达标人数统计
                'qualification_stats' => [
                    'vip' => [
                        'junior' => $vipJuniorQualified,
                        'middle' => $vipMiddleQualified,
                        'senior' => $vipSeniorQualified
                    ],
                    'recharge' => [
                        'junior' => $rechargeJuniorQualified,
                        'middle' => $rechargeMiddleQualified,
                        'senior' => $rechargeSeniorQualified
                    ]
                ],
                
                // 分红分配（简化版）
                'dividend_distribution' => [
                    'vip' => [
                        'junior' => [
                            'pool' => $vipJuniorQualified > 0 ? $vipPoolAmount / 3 : 0,
                            'qualified_count' => $vipJuniorQualified,
                            'per_person' => $vipJuniorQualified > 0 ? ($vipPoolAmount / 3) / max($vipJuniorQualified, 1) : 0
                        ],
                        'middle' => [
                            'pool' => $vipMiddleQualified > 0 ? $vipPoolAmount / 3 : 0,
                            'qualified_count' => $vipMiddleQualified,
                            'per_person' => $vipMiddleQualified > 0 ? ($vipPoolAmount / 3) / max($vipMiddleQualified, 1) : 0
                        ],
                        'senior' => [
                            'pool' => $vipSeniorQualified > 0 ? $vipPoolAmount / 3 : 0,
                            'qualified_count' => $vipSeniorQualified,
                            'per_person' => '按直推占比分配'
                        ]
                    ],
                    'recharge' => [
                        'junior' => [
                            'pool' => $rechargeJuniorQualified > 0 ? $rechargePoolAmount / 3 : 0,
                            'qualified_count' => $rechargeJuniorQualified,
                            'per_person' => $rechargeJuniorQualified > 0 ? ($rechargePoolAmount / 3) / max($rechargeJuniorQualified, 1) : 0
                        ],
                        'middle' => [
                            'pool' => $rechargeMiddleQualified > 0 ? $rechargePoolAmount / 3 : 0,
                            'qualified_count' => $rechargeMiddleQualified,
                            'per_person' => $rechargeMiddleQualified > 0 ? ($rechargePoolAmount / 3) / max($rechargeMiddleQualified, 1) : 0
                        ],
                        'senior' => [
                            'pool' => $rechargeSeniorQualified > 0 ? $rechargePoolAmount / 3 : 0,
                            'qualified_count' => $rechargeSeniorQualified,
                            'per_person' => '按直推占比分配'
                        ]
                    ]
                ]
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $responseData
            ]);
            
        } catch (\Exception $e) {
            Log::error("获取分支机构VIP分红明细失败", [
                'error' => $e->getMessage(),
                'user_id' => $userId ?? null,
                'branch_code' => $branchCode ?? null,
                'month' => $month ?? null
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 隐藏用户姓名
     */
    private function hideUserName($name)
    {
        if (!$name || strlen($name) <= 1) {
            return $name;
        }
        
        $len = mb_strlen($name, 'UTF-8');
        if ($len <= 2) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*';
        } else {
            return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8');
        }
    }
} 